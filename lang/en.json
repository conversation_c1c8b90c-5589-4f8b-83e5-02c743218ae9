{"users.navigation_label": "Users", "users.navigation_group": "User Management", "users.model_label": "User", "users.plural_model_label": "Users", "users.form.section_title": "User Information", "users.form.section_description": "Manage user account details", "users.form.fields.name.label": "Name", "users.form.fields.name.placeholder": "Enter full name", "users.form.fields.name.helper_text": "The user's full name", "users.form.fields.email.label": "Email", "users.form.fields.email.placeholder": "<EMAIL>", "users.form.fields.email.helper_text": "Must be a valid email address", "users.form.fields.email_verified_at.label": "Email Verified At", "users.form.fields.email_verified_at.helper_text": "When the user verified their email address", "users.form.fields.password.label": "Password", "users.form.fields.password.placeholder": "Enter password", "users.form.fields.password.helper_text": "Minimum 8 characters required", "users.table.columns.name": "Name", "users.table.columns.email": "Email", "users.table.columns.email_verified": "Verified", "users.table.columns.created_at": "Created", "users.table.columns.updated_at": "Updated", "users.table.filters.email_verified.label": "<PERSON><PERSON>", "users.table.filters.email_verified.placeholder": "All users", "users.table.filters.email_verified.true_label": "Verified users", "users.table.filters.email_verified.false_label": "Unverified users", "users.table.actions.view": "View", "users.table.actions.edit": "Edit", "users.table.actions.delete": "Delete", "users.table.bulk_actions.delete": "Delete Selected", "users.table.messages.email_copied": "Email address copied", "users.pages.list.title": "Users", "users.pages.list.navigation_label": "All Users", "users.pages.create.title": "Create User", "users.pages.create.navigation_label": "New User", "users.pages.edit.title": "Edit User", "users.pages.edit.navigation_label": "Edit", "users.pages.view.title": "View User", "users.pages.view.navigation_label": "View", "users.notifications.created": "User created successfully", "users.notifications.updated": "User updated successfully", "users.notifications.deleted": "User deleted successfully"}