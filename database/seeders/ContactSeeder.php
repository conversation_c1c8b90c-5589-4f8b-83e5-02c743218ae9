<?php

namespace Database\Seeders;

use App\Models\Contact;
use App\Models\ContactGroup;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ContactSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $contacts = [
            [
                'name' => '<PERSON>',
                'whatsapp_number' => '+1234567890',
                'company' => 'Tech Solutions Inc.',
                'notes' => 'Lead developer and project manager',
                'groups' => ['Work Colleagues', 'Clients'],
            ],
            [
                'name' => '<PERSON>',
                'whatsapp_number' => '+1234567891',
                'company' => 'Marketing Pro',
                'notes' => 'Marketing specialist',
                'groups' => ['Clients'],
            ],
            [
                'name' => '<PERSON>',
                'whatsapp_number' => '+1234567892',
                'company' => null,
                'notes' => 'Brother, emergency contact',
                'groups' => ['Family', 'Emergency Contacts'],
            ],
            [
                'name' => '<PERSON>',
                'whatsapp_number' => '+1234567893',
                'company' => 'Design Studio',
                'notes' => 'Graphic designer and friend',
                'groups' => ['Friends', 'Work Colleagues'],
            ],
            [
                'name' => '<PERSON>',
                'whatsapp_number' => '+1234567894',
                'company' => 'Supply Chain Co.',
                'notes' => 'Main supplier for office materials',
                'groups' => ['Suppliers'],
            ],
            [
                'name' => 'Lisa Anderson',
                'whatsapp_number' => '+1234567895',
                'company' => null,
                'notes' => 'College friend',
                'groups' => ['Friends'],
            ],
        ];

        foreach ($contacts as $contactData) {
            $groups = $contactData['groups'];
            unset($contactData['groups']);

            $contact = Contact::create($contactData);

            // Attach to groups
            $groupIds = ContactGroup::whereIn('name', $groups)->pluck('id');
            $contact->contactGroups()->attach($groupIds);
        }
    }
}
