<?php

namespace Database\Seeders;

use App\Models\ContactGroup;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ContactGroupSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $groups = [
            [
                'name' => 'Family',
                'description' => 'Family members and close relatives',
            ],
            [
                'name' => 'Work Colleagues',
                'description' => 'Professional contacts and work colleagues',
            ],
            [
                'name' => 'Clients',
                'description' => 'Business clients and customers',
            ],
            [
                'name' => 'Friends',
                'description' => 'Personal friends and social contacts',
            ],
            [
                'name' => 'Suppliers',
                'description' => 'Business suppliers and vendors',
            ],
            [
                'name' => 'Emergency Contacts',
                'description' => 'Important emergency contacts',
            ],
        ];

        foreach ($groups as $group) {
            ContactGroup::create($group);
        }
    }
}
