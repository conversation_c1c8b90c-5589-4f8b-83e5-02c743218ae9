<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Contact extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'whatsapp_number',
        'company',
        'notes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Get the contact groups that this contact belongs to.
     */
    public function contactGroups(): BelongsToMany
    {
        return $this->belongsToMany(ContactGroup::class, 'contact_group_contact');
    }

    /**
     * Get the formatted WhatsApp URL for this contact.
     */
    public function getWhatsappUrlAttribute(): string
    {
        $number = preg_replace('/[^0-9]/', '', $this->whatsapp_number);
        return "https://wa.me/{$number}";
    }

    /**
     * Get the formatted WhatsApp number.
     */
    public function getFormattedWhatsappNumberAttribute(): string
    {
        $number = $this->whatsapp_number;

        // Basic formatting for international numbers
        if (strlen($number) >= 10) {
            return preg_replace('/(\d{1,3})(\d{3})(\d{3})(\d{4})/', '+$1 $2 $3 $4', $number);
        }

        return $number;
    }
}
