<?php

namespace App\Filament\Resources;

use App\Filament\Resources\UserResource\Pages;
use App\Filament\Resources\UserResource\RelationManagers;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Hash;

class UserResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';

    protected static ?int $navigationSort = 1;

    public static function getNavigationLabel(): string
    {
        return __('users.navigation_label');
    }

    public static function getModelLabel(): string
    {
        return __('users.model_label');
    }

    public static function getPluralModelLabel(): string
    {
        return __('users.plural_model_label');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make(__('users.form.section_title'))
                    ->description(__('users.form.section_description'))
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label(__('users.form.fields.name.label'))
                            ->required()
                            ->maxLength(255)
                            ->placeholder(__('users.form.fields.name.placeholder'))
                            ->helperText(__('users.form.fields.name.helper_text')),

                        Forms\Components\TextInput::make('email')
                            ->label(__('users.form.fields.email.label'))
                            ->email()
                            ->required()
                            ->maxLength(255)
                            ->unique(User::class, 'email', ignoreRecord: true)
                            ->placeholder(__('users.form.fields.email.placeholder'))
                            ->helperText(__('users.form.fields.email.helper_text')),

                        Forms\Components\DateTimePicker::make('email_verified_at')
                            ->label(__('users.form.fields.email_verified_at.label'))
                            ->helperText(__('users.form.fields.email_verified_at.helper_text'))
                            ->displayFormat('M j, Y g:i A')
                            ->nullable(),

                        Forms\Components\TextInput::make('password')
                            ->label(__('users.form.fields.password.label'))
                            ->password()
                            ->required(fn(string $context): bool => $context === 'create')
                            ->minLength(8)
                            ->maxLength(255)
                            ->dehydrateStateUsing(fn($state) => Hash::make($state))
                            ->dehydrated(fn($state) => filled($state))
                            ->placeholder(__('users.form.fields.password.placeholder'))
                            ->helperText(__('users.form.fields.password.helper_text'))
                            ->revealable(),
                    ])
                    ->columns(1),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label(__('users.table.columns.name'))
                    ->searchable()
                    ->sortable()
                    ->weight('medium'),

                Tables\Columns\TextColumn::make('email')
                    ->label(__('users.table.columns.email'))
                    ->searchable()
                    ->sortable()
                    ->copyable()
                    ->copyMessage(__('users.table.messages.email_copied'))
                    ->icon('heroicon-m-envelope'),

                Tables\Columns\IconColumn::make('email_verified_at')
                    ->label(__('users.table.columns.email_verified'))
                    ->boolean()
                    ->trueIcon('heroicon-o-check-badge')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger')
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('users.table.columns.created_at'))
                    ->dateTime('M j, Y')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('users.table.columns.updated_at'))
                    ->dateTime('M j, Y')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('email_verified_at')
                    ->label(__('users.table.filters.email_verified.label'))
                    ->nullable()
                    ->placeholder(__('users.table.filters.email_verified.placeholder'))
                    ->trueLabel(__('users.table.filters.email_verified.true_label'))
                    ->falseLabel(__('users.table.filters.email_verified.false_label')),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->label(__('users.table.actions.view')),
                Tables\Actions\EditAction::make()
                    ->label(__('users.table.actions.edit')),
                Tables\Actions\DeleteAction::make()
                    ->label(__('users.table.actions.delete')),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->label(__('users.table.bulk_actions.delete')),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUsers::route('/'),
            'create' => Pages\CreateUser::route('/create'),
            'view' => Pages\ViewUser::route('/{record}'),
            'edit' => Pages\EditUser::route('/{record}/edit'),
        ];
    }
}
