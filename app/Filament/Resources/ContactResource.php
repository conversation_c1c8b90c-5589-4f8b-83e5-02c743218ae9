<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ContactResource\Pages;
use App\Filament\Resources\ContactResource\RelationManagers;
use App\Models\Contact;
use App\Models\ContactGroup;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Validation\Rule;

class ContactResource extends Resource
{
    protected static ?string $model = Contact::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';

    protected static ?string $navigationGroup = 'Contacts';

    protected static ?int $navigationSort = 1;

    public static function getNavigationLabel(): string
    {
        return 'Contacts';
    }

    public static function getModelLabel(): string
    {
        return 'Contact';
    }

    public static function getPluralModelLabel(): string
    {
        return 'Contacts';
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Contact Details')
                    ->description('Basic contact information')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('Full Name')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('Enter contact\'s full name')
                            ->helperText('The contact\'s full name'),

                        Forms\Components\TextInput::make('whatsapp_number')
                            ->label('WhatsApp Number')
                            ->required()
                            ->unique(Contact::class, 'whatsapp_number', ignoreRecord: true)
                            ->maxLength(255)
                            ->placeholder('+1234567890')
                            ->helperText('International format recommended (e.g., +1234567890)')
                            ->rules([
                                'regex:/^[\+]?[1-9][\d]{0,15}$/',
                            ])
                            ->validationMessages([
                                'regex' => 'Please enter a valid WhatsApp number in international format.',
                            ]),

                        Forms\Components\TextInput::make('company')
                            ->label('Company')
                            ->maxLength(255)
                            ->placeholder('Company name (optional)')
                            ->helperText('The company this contact works for'),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Additional Information')
                    ->description('Extra details and group assignments')
                    ->schema([
                        Forms\Components\Textarea::make('notes')
                            ->label('Notes')
                            ->placeholder('Add any additional notes about this contact...')
                            ->helperText('Any additional information about this contact')
                            ->rows(4)
                            ->columnSpanFull(),

                        Forms\Components\Select::make('contactGroups')
                            ->label('Contact Groups')
                            ->relationship('contactGroups', 'name')
                            ->multiple()
                            ->preload()
                            ->searchable()
                            ->placeholder('Select contact groups')
                            ->helperText('Assign this contact to one or more groups')
                            ->columnSpanFull(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Name')
                    ->searchable()
                    ->sortable()
                    ->weight('medium'),

                Tables\Columns\TextColumn::make('whatsapp_number')
                    ->label('WhatsApp Number')
                    ->searchable()
                    ->sortable()
                    ->copyable()
                    ->copyMessage('WhatsApp number copied')
                    ->icon('heroicon-m-phone'),

                Tables\Columns\TextColumn::make('company')
                    ->label('Company')
                    ->searchable()
                    ->sortable()
                    ->placeholder('No company')
                    ->icon('heroicon-m-building-office'),

                Tables\Columns\TextColumn::make('contactGroups.name')
                    ->label('Groups')
                    ->badge()
                    ->searchable()
                    ->placeholder('No groups')
                    ->separator(','),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime('M j, Y')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Updated')
                    ->dateTime('M j, Y')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('contactGroups')
                    ->label('Contact Group')
                    ->relationship('contactGroups', 'name')
                    ->multiple()
                    ->preload(),

                Tables\Filters\Filter::make('has_company')
                    ->label('Has Company')
                    ->query(fn(Builder $query): Builder => $query->whereNotNull('company')),

                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\Action::make('whatsapp')
                    ->label('WhatsApp')
                    ->icon('heroicon-o-chat-bubble-left-right')
                    ->color('success')
                    ->url(fn(Contact $record): string => $record->whatsapp_url)
                    ->openUrlInNewTab(),
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('name', 'asc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListContacts::route('/'),
            'create' => Pages\CreateContact::route('/create'),
            'view' => Pages\ViewContact::route('/{record}'),
            'edit' => Pages\EditContact::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
