<?php

namespace App\Filament\Resources\ContactGroupResource\RelationManagers;

use App\Models\Contact;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ContactsRelationManager extends RelationManager
{
    protected static string $relationship = 'contacts';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label('Full Name')
                    ->required()
                    ->maxLength(255)
                    ->placeholder('Enter contact\'s full name'),

                Forms\Components\TextInput::make('whatsapp_number')
                    ->label('WhatsApp Number')
                    ->required()
                    ->unique(Contact::class, 'whatsapp_number', ignoreRecord: true)
                    ->maxLength(255)
                    ->placeholder('+1234567890')
                    ->rules([
                        'regex:/^[\+]?[1-9][\d]{0,15}$/',
                    ]),

                Forms\Components\TextInput::make('company')
                    ->label('Company')
                    ->maxLength(255)
                    ->placeholder('Company name (optional)'),

                Forms\Components\Textarea::make('notes')
                    ->label('Notes')
                    ->placeholder('Add any additional notes...')
                    ->rows(3)
                    ->columnSpanFull(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Name')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('whatsapp_number')
                    ->label('WhatsApp')
                    ->searchable()
                    ->copyable()
                    ->icon('heroicon-m-phone'),

                Tables\Columns\TextColumn::make('company')
                    ->label('Company')
                    ->searchable()
                    ->placeholder('No company'),
            ])
            ->filters([
                Tables\Filters\Filter::make('has_company')
                    ->label('Has Company')
                    ->query(fn(Builder $query): Builder => $query->whereNotNull('company')),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
                Tables\Actions\AttachAction::make()
                    ->recordSelectSearchColumns(['name', 'whatsapp_number', 'company']),
            ])
            ->actions([
                Tables\Actions\Action::make('whatsapp')
                    ->label('WhatsApp')
                    ->icon('heroicon-o-chat-bubble-left-right')
                    ->color('success')
                    ->url(fn(Contact $record): string => $record->whatsapp_url)
                    ->openUrlInNewTab(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DetachAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DetachBulkAction::make(),
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('name', 'asc');
    }
}
