<?php

namespace App\Filament\Resources\ContactResource\Pages;

use App\Filament\Resources\ContactResource;
use App\Models\Contact;
use App\Models\ContactGroup;
use Filament\Actions;
use Filament\Forms;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Support\Collection;

class ListContacts extends ListRecords
{
    protected static string $resource = ContactResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),

            Actions\Action::make('export')
                ->label('Export CSV')
                ->icon('heroicon-o-arrow-down-tray')
                ->color('success')
                ->action(function () {
                    return $this->exportContacts();
                }),

            Actions\Action::make('import')
                ->label('Import CSV')
                ->icon('heroicon-o-arrow-up-tray')
                ->color('primary')
                ->form([
                    Forms\Components\FileUpload::make('file')
                        ->label('CSV File')
                        ->acceptedFileTypes(['text/csv', 'application/csv'])
                        ->required()
                        ->helperText('Upload a CSV file with columns: name, whatsapp_number, company, notes, groups'),
                ])
                ->action(function (array $data) {
                    return $this->importContacts($data['file']);
                }),
        ];
    }

    protected function exportContacts()
    {
        $contacts = Contact::with('contactGroups')->get();

        $csvData = [];
        $csvData[] = ['Name', 'WhatsApp Number', 'Company', 'Notes', 'Groups'];

        foreach ($contacts as $contact) {
            $groups = $contact->contactGroups->pluck('name')->join(', ');
            $csvData[] = [
                $contact->name,
                $contact->whatsapp_number,
                $contact->company ?? '',
                $contact->notes ?? '',
                $groups,
            ];
        }

        $filename = 'contacts_' . now()->format('Y-m-d_H-i-s') . '.csv';
        $filePath = storage_path('app/public/' . $filename);

        $file = fopen($filePath, 'w');
        foreach ($csvData as $row) {
            fputcsv($file, $row);
        }
        fclose($file);

        $this->notify('success', 'Contacts exported successfully!');

        return response()->download($filePath)->deleteFileAfterSend();
    }

    protected function importContacts($filePath)
    {
        $fullPath = storage_path('app/public/' . $filePath);

        if (!file_exists($fullPath)) {
            $this->notify('danger', 'File not found!');
            return;
        }

        $file = fopen($fullPath, 'r');
        $header = fgetcsv($file); // Skip header row

        $imported = 0;
        $errors = [];

        while (($row = fgetcsv($file)) !== false) {
            try {
                if (count($row) < 2) continue; // Skip incomplete rows

                $contact = Contact::updateOrCreate(
                    ['whatsapp_number' => $row[1]], // Use WhatsApp number as unique identifier
                    [
                        'name' => $row[0] ?? '',
                        'company' => $row[2] ?? null,
                        'notes' => $row[3] ?? null,
                    ]
                );

                // Handle groups if provided
                if (!empty($row[4])) {
                    $groupNames = array_map('trim', explode(',', $row[4]));
                    $groupIds = [];

                    foreach ($groupNames as $groupName) {
                        if (!empty($groupName)) {
                            $group = ContactGroup::firstOrCreate(['name' => $groupName]);
                            $groupIds[] = $group->id;
                        }
                    }

                    if (!empty($groupIds)) {
                        $contact->contactGroups()->sync($groupIds);
                    }
                }

                $imported++;
            } catch (\Exception $e) {
                $errors[] = "Row " . ($imported + 1) . ": " . $e->getMessage();
            }
        }

        fclose($file);

        if ($imported > 0) {
            $this->notify('success', "Successfully imported {$imported} contacts!");
        }

        if (!empty($errors)) {
            $this->notify('warning', 'Some rows had errors: ' . implode(', ', array_slice($errors, 0, 3)));
        }
    }
}
